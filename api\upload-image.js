// api/upload-image.js
const { put } = require('@vercel/blob');
const { formidable } = require('formidable');
const fs = require('fs');
const { lookup } = require('mime-types');

// ────────────────────────────────────────────────
// Sharp configuration for serverless environment
// Optimized for reliability over performance
const sharp = require('sharp');
sharp.cache(false); // Disable cache in serverless
sharp.concurrency(1); // Single-threaded for reliability
const fsPromises = require('fs/promises');
// ────────────────────────────────────────────────

const MAX_UPLOAD = 4 * 1024 * 1024; // 4MB
const MAX_DIMENSIONS = 2000; // Max width/height in pixels
const ALLOWED_MIME = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
// NEW: Add a mapping for sharp format verification
const ALLOWED_SHARP_FORMATS = ['jpeg', 'png', 'webp', 'gif'];

// ✅ SIMPLIFIED: Basic upload tracking for concurrency control
const activeUploads = new Set();
const MAX_CONCURRENT_UPLOADS = 1; // Single upload at a time for reliability

module.exports = async (req, res) => {
    console.log('[upload-image] Request received:', req.method);

    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end('Method Not Allowed');
    }

    // ✅ SIMPLIFIED: Basic concurrency check
    if (activeUploads.size >= MAX_CONCURRENT_UPLOADS) {
        return res.status(429).json({
            message: 'Upload in progress. Please wait and try again.',
        });
    }

    const uploadId = `upload_${Date.now()}`;
    activeUploads.add(uploadId);
    console.log(`🔄 Starting upload ${uploadId}`);

    let uploadedFile = null; // Declare in broader scope for cleanup in catch block

    try {
        const form = formidable({
            keepExtensions: true,
            maxFileSize: MAX_UPLOAD,
        });

        const [fields, files] = await new Promise((resolve, reject) => {
            form.parse(req, (err, fields, files) => {
                if (err) {
                    reject(err);
                } else {
                    resolve([fields, files]);
                }
            });
        });

        const fileObj = files.file || files.image;
        uploadedFile = Array.isArray(fileObj) ? fileObj[0] : fileObj;
        if (!uploadedFile) {
            return res.status(400).json({ message: 'No file provided' });
        }

        // ─── NEW: BULLET-PROOF INTEGRITY CHECKS ───────────────────────────────
        // These checks run BEFORE we attempt to read or process the file.

        // CHECK 1: Was the file truncated by Formidable for being too large?
        if (uploadedFile.truncated) {
            console.error(`Upload truncated: File exceeded ${MAX_UPLOAD} bytes.`);
            return res.status(413).json({
                message: `File is too large. The limit is ${MAX_UPLOAD / 1024 / 1024}MB.`,
            });
        }

        // CHECK 2: Does the file size on disk match the expected size?
        // This prevents race conditions where we read a file that's still being written.
        const stats = await fsPromises.stat(uploadedFile.filepath);
        if (stats.size !== uploadedFile.size) {
            console.error(
                `Upload size mismatch: on-disk(${stats.size}) vs expected(${uploadedFile.size}).`
            );
            return res.status(500).json({
                message: 'Incomplete upload due to a server issue. Please try again.',
            });
        }

        console.log(
            `✅ File integrity verified: ${uploadedFile.originalFilename} (${stats.size} bytes)`
        );
        // ──────────────────────────────────────────────────────────────────

        let mime = uploadedFile.mimetype || '';
        if (mime === '' || mime.toLowerCase() === 'application/octet-stream') {
            mime = lookup(uploadedFile.originalFilename) || '';
        }

        if (!ALLOWED_MIME.includes(mime.toLowerCase())) {
            return res.status(415).json({
                message: 'Unsupported image type. Please use JPG, PNG, WebP, or GIF.',
            });
        }
        uploadedFile.mimetype = mime; // Normalise for later put

        // This check is now redundant because of the `uploadedFile.truncated` check,
        // but we'll leave it as a failsafe.
        if (uploadedFile.size > MAX_UPLOAD) {
            return res.status(413).json({
                message: `File too large. Maximum size is ${MAX_UPLOAD / 1024 / 1024}MB`,
            });
        }

        // ─── NEW: Sanitized Filename Generation ────────────────────────────
        const originalName = uploadedFile.originalFilename || 'image';
        const clean = originalName
            .toLowerCase()
            .replace(/\.\w+$/, '') // remove original extension
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');

        // Derive the extension from the trusted MIME type
        const extension = mime.split('/')[1] || 'jpg';
        const filename = `${Date.now()}-${clean}.${extension}`;
        // ──────────────────────────────────────────────────────────────────

        // Robust folder parsing - handles both 'team' and ['team'] formats
        const folderField = Array.isArray(fields.folder) ? fields.folder[0] : fields.folder;
        const folder = (folderField || 'content-images').trim();

        /* -------------------------------------------------------------
            1️⃣  Read the *fully-verified* temp file into RAM
        ------------------------------------------------------------- */
        const buffer = await fsPromises.readFile(uploadedFile.filepath);

        // CHECK 3: Verify buffer integrity - ensure buffer size matches file size
        if (buffer.length !== uploadedFile.size) {
            console.error(
                `Buffer size mismatch: buffer(${buffer.length}) vs file(${uploadedFile.size})`
            );
            // Clean up temp file before exiting
            fs.unlink(uploadedFile.filepath, err => {
                if (err) {
                    console.error('Error deleting incomplete temp file:', err);
                }
            });
            return res.status(500).json({
                message: 'File read error. Please try uploading again.',
            });
        }

        // ✅ SIMPLIFIED: Basic Sharp initialization for metadata
        const img = sharp(buffer, {
            failOnError: true, // Fail fast on real errors
            sequentialRead: true,
            limitInputPixels: MAX_DIMENSIONS * MAX_DIMENSIONS * 4,
        });

        /* -------------------------------------------------------------
            2️⃣  Metadata check
        ------------------------------------------------------------- */
        let metadata;
        try {
            metadata = await img.metadata();
        } catch (e) {
            console.warn('[upload-image] Sharp metadata failed:', e.message);
            // Delete the invalid temp file before exiting
            fs.unlink(uploadedFile.filepath, err => {
                if (err) {
                    console.error('Error deleting corrupt temp file:', err);
                }
            });
            return res.status(400).json({
                message: 'Unable to process image – it may be corrupt or not a valid image file.',
            });
        }

        // ─── NEW: Verify Sharp-detected format ──────────────────────────────
        if (!metadata.format || !ALLOWED_SHARP_FORMATS.includes(metadata.format)) {
            fs.unlink(uploadedFile.filepath, () => {}); // cleanup
            return res.status(415).json({
                message: `File appears to be a ${metadata.format || 'unknown type'}, not an allowed image.`,
            });
        }

        console.log(`✅ Image validated: ${metadata.width}x${metadata.height} ${metadata.format}`);

        // ✅ NEW: Additional validation to detect corrupted/incomplete images
        if (!metadata.width || !metadata.height || metadata.width < 1 || metadata.height < 1) {
            fs.unlink(uploadedFile.filepath, () => {}); // cleanup
            return res.status(400).json({
                message: 'Image appears to be corrupted or has invalid dimensions.',
            });
        }

        // ✅ IMPROVED: Minimal size validation - only catch truly corrupted files
        // Modern JPEG compression can achieve very high ratios, so be very conservative
        const minFileSize = 50; // Absolute minimum - anything smaller is likely corrupted

        // Only flag files that are impossibly small (likely truncated during upload)
        if (buffer.length < minFileSize) {
            console.warn(
                `File too small: ${buffer.length} bytes for ${metadata.width}x${metadata.height} image`
            );
            fs.unlink(uploadedFile.filepath, () => {}); // cleanup
            return res.status(400).json({
                message:
                    'Image file appears to be corrupted or incomplete. Please try uploading again.',
            });
        }

        console.log(
            `✅ Size validation passed: ${buffer.length} bytes for ${metadata.width}x${metadata.height}`
        );
        // ──────────────────────────────────────────────────────────────────

        if (metadata.width > MAX_DIMENSIONS || metadata.height > MAX_DIMENSIONS) {
            return res.status(400).json({
                message: `Image dimensions too large. Max ${MAX_DIMENSIONS}px each side.`,
            });
        }

        /* -------------------------------------------------------------
            3️⃣  Server-side image processing & validation
        ------------------------------------------------------------- */

        // ✅ SIMPLIFIED: Process image through Sharp with minimal operations
        // Single-pass processing to prevent corruption
        let processedBuffer;
        try {
            console.log('🔄 Processing image through Sharp...');

            // Single Sharp instance with minimal configuration
            const img = sharp(buffer, {
                failOnError: true, // Fail fast on real errors
                sequentialRead: true,
                limitInputPixels: MAX_DIMENSIONS * MAX_DIMENSIONS * 4,
            });

            // Get metadata for validation
            const processMetadata = await img.metadata();
            if (!processMetadata.width || !processMetadata.height) {
                throw new Error('Invalid image metadata');
            }

            console.log(`📐 Processing ${processMetadata.width}x${processMetadata.height} ${processMetadata.format} image`);

            // Simple processing: just auto-rotate and ensure format consistency
            processedBuffer = await img
                .rotate() // Auto-rotate based on EXIF data
                .toBuffer();

            // Basic validation
            if (!processedBuffer || processedBuffer.length === 0) {
                throw new Error('Image processing failed');
            }

            console.log(`✅ Image processed: ${processedBuffer.length} bytes`);

        } catch (processError) {
            console.error('Image processing failed:', processError.message);
            fs.unlink(uploadedFile.filepath, () => {}); // cleanup
            return res.status(500).json({
                message: 'Unable to process this image. Please try a different image.',
                error: processError.message
            });
        }

        /* -------------------------------------------------------------
            4️⃣  Thumbnail generation with improved error handling
        ------------------------------------------------------------- */

        let thumbnailBuffer;
        try {
            console.log('🖼️ Generating thumbnail...');

            // Simple thumbnail generation using the original buffer
            // Avoid creating multiple Sharp instances
            thumbnailBuffer = await sharp(buffer)
                .resize(240, 240, {
                    fit: 'cover',
                    position: 'center',
                })
                .jpeg({ quality: 80 })
                .toBuffer();

            if (!thumbnailBuffer || thumbnailBuffer.length === 0) {
                throw new Error('Thumbnail generation failed');
            }

            console.log(`✅ Thumbnail generated: ${thumbnailBuffer.length} bytes`);

        } catch (thumbError) {
            console.error('Thumbnail generation failed:', thumbError.message);
            fs.unlink(uploadedFile.filepath, () => {}); // cleanup
            return res.status(500).json({
                message: 'Failed to generate thumbnail. Please try a different image.',
                error: thumbError.message
            });
        }

        const putOpts = { access: 'public', contentType: uploadedFile.mimetype, overwrite: true };
        const mainKey = `${folder}/${filename}`;
        const thumbKey = `${folder}/thumbnails/${filename}`;

        // ✅ SIMPLIFIED: Direct uploads with basic error handling
        console.log('🔄 Uploading to blob storage...');

        let url, thumbnailUrl;
        try {
            // Upload main image
            console.log('📤 Uploading main image...');
            const uploadResult = await put(mainKey, processedBuffer, putOpts);
            url = uploadResult.url;
            console.log(`✅ Main image uploaded: ${url}`);

            // Upload thumbnail
            console.log('📤 Uploading thumbnail...');
            const thumbResult = await put(thumbKey, thumbnailBuffer, putOpts);
            thumbnailUrl = thumbResult.url;
            console.log(`✅ Thumbnail uploaded: ${thumbnailUrl}`);

            console.log(`✅ All uploads complete: ${url}`);
        } catch (uploadError) {
            console.error('Upload to blob storage failed:', uploadError.message);
            fs.unlink(uploadedFile.filepath, () => {}); // cleanup
            activeUploads.delete(uploadId);
            return res.status(500).json({
                message: 'Failed to upload image to storage. Please try again.',
                error: uploadError.message
            });
        }

        // Clean up temp file
        fs.unlink(uploadedFile.filepath, err => {
            if (err) {
                console.error('Error deleting temp file:', err);
            }
        });

        // ✅ CLEANUP: Remove from active uploads
        activeUploads.delete(uploadId);
        console.log(`✅ Upload ${uploadId} completed successfully`);

        return res.status(200).json({
            url,
            thumb: thumbnailUrl,
            width: metadata.width,
            height: metadata.height,
            type: uploadedFile.mimetype,
        });
    } catch (error) {
        console.error('[upload-image] Unexpected error:', error);
        console.error('[upload-image] Error stack:', error.stack);
        console.error('[upload-image] Error name:', error.name);
        console.error('[upload-image] Error code:', error.code);

        // ✅ CLEANUP: Remove from active uploads on error
        activeUploads.delete(uploadId);
        console.log(`❌ Upload ${uploadId} failed and cleaned up`);

        // Clean up temp file if it exists
        if (uploadedFile?.filepath) {
            fs.unlink(uploadedFile.filepath, err => {
                if (err) {
                    console.error('Error deleting temp file after error:', err);
                }
            });
        }

        return res.status(500).json({
            message: 'Upload failed unexpectedly',
            error: error.message,
        });
    }
};

// Pin runtime for Sharp compatibility
module.exports.config = { runtime: 'nodejs18.x' };
